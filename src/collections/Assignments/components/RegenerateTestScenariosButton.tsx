'use client';

import { Button, toast } from '@payloadcms/ui';
import { RefreshCw } from 'lucide-react';
import { useParams, useRouter } from 'next/navigation';
import type { UIFieldClientComponent } from 'payload';
import { useState } from 'react';

const RegenerateTestScenariosButton: UIFieldClientComponent = () => {
  const router = useRouter();
  const params = useParams();
  const [loading, setLoading] = useState(false);

  const assignmentId = params?.id as string;

  const handleRegenerate = async () => {
    if (!assignmentId) {
      toast.error('Assignment ID not found');
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`/api/assignments/${assignmentId}/regenerate-test-scenarios`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || `Failed to regenerate test scenarios: ${response.statusText}`);
      }

      const result = await response.json();

      toast.success('Test scenarios regenerated successfully!');

      // Refresh the page to show the updated test scenarios
      router.refresh();

    } catch (error) {
      console.error('Failed to regenerate test scenarios:', error);
      toast.error(
        error instanceof Error
          ? error.message
          : 'An unexpected error occurred while regenerating test scenarios'
      );
    } finally {
      setLoading(false);
    }
  };

  // Only show the button if we have an assignment ID (i.e., we're editing an existing assignment)
  if (!assignmentId || assignmentId === 'create') {
    return null;
  }

  return (
    <Button
      buttonStyle="secondary"
      onClick={handleRegenerate}
      disabled={loading}
      size="medium"
      icon={<RefreshCw size={16} className={loading ? 'animate-spin' : ''} />}
    // style={{
    //   display: 'flex',
    //   alignItems: 'center',
    //   gap: '.5rem',
    // }}
    >
      {loading ? 'Regenerating...' : 'Regenerate Test Scenarios'}
    </Button>
  );
};

export default RegenerateTestScenariosButton;
