'use client';

import './EvaluateButton.css';

import { Button, useDocumentInfo } from '@payloadcms/ui';
import { useRouter } from 'next/navigation';
import type { UIFieldClientComponent } from 'payload';
import { useJobPolling } from '@/hooks/useJobPolling';

const RegenerateTestsButton: UIFieldClientComponent = () => {
  const router = useRouter();
  const { id } = useDocumentInfo();

  const { status, startJob, reset } = useJobPolling({
    onComplete: () => {
      router.refresh();
    },
  });

  const startRegenerateJob = async () => {
    await startJob(`/api/submissions/${id}/regenerate-tests`);
  };

  const getButtonText = () => {
    switch (status) {
      case 'processing':
        return 'Regenerating...';
      case 'completed':
        return 'Tests Regenerated';
      case 'error':
        return 'Error - Retry';
      default:
        return 'Regenerate Test Scenarios';
    }
  };

  const handleButtonClick = () => {
    if (status === 'completed') {
      reset(); // Reset to allow re-regeneration
    } else if (status === 'error') {
      reset();
      startRegenerateJob();
    } else {
      startRegenerateJob();
    }
  };

  const getButtonClassName = () => {
    switch (status) {
      case 'completed':
        return 'btn btn--style-success btn--size-medium';
      case 'error':
        return 'btn btn--style-error btn--size-medium';
      default:
        return 'btn btn--style-secondary btn--size-medium';
    }
  };

  return (
    <Button
      onClick={handleButtonClick}
      disabled={status === 'processing'}
      className={getButtonClassName()}
      size="medium"
    >
      {getButtonText()}
    </Button>
  );
};

export default RegenerateTestsButton;
